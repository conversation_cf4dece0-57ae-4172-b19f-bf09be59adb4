# API 接口分离设计文档

## 概述

本项目实现了基于客户端类型的API接口分离，确保不同客户端（admin-system、uniapp等）使用适合的API接口，避免耦合并提高安全性。

## 客户端类型

### 支持的客户端类型

1. **ADMIN_WEB** - 管理后台Web客户端
2. **UNIAPP** - UniApp移动应用
3. **MOBILE_APP** - 原生移动应用
4. **THIRD_PARTY** - 第三方应用
5. **UNKNOWN** - 未知客户端

### 客户端检测机制

#### 1. 自定义Header检测（推荐）

```http
X-Client-Type: uniapp
X-Client-Version: 1.0.0
X-Client-Platform: android
```

#### 2. User-Agent检测

- `uni-app` → UNIAPP
- `AdminSystem` → ADMIN_WEB
- `Mobile|Android|iPhone|iPad` → MOBILE_APP

## API 路由分离

### 管理端API（仅限admin-web客户端）

```
/api/users/*          - 用户管理
/api/admin/*          - 管理功能
/api/analytics/*      - 数据分析
/api/system/*         - 系统配置
```

**特点：**
- 需要管理员权限
- 仅允许admin-web客户端访问
- 完整的CRUD操作
- 详细的审计日志

### 移动端API（移动客户端专用）

```
/api/mobile/auth/*    - 移动端认证
/api/mobile/user/*    - 移动端用户功能
/api/mobile/content/* - 移动端内容
/api/mobile/upload/*  - 移动端上传
```

**特点：**
- 简化的数据结构
- 移动端优化的响应
- 更长的token有效期
- 设备信息记录

### 通用API（所有客户端）

```
/api/auth/login       - 通用登录（已废弃，建议使用专用接口）
/api/public/*         - 公开接口
```

## 权限控制

### 客户端权限矩阵

| 功能 | ADMIN_WEB | UNIAPP | MOBILE_APP | THIRD_PARTY |
|------|-----------|--------|------------|-------------|
| 访问管理API | ✅ | ❌ | ❌ | ❌ |
| 访问用户API | ✅ | ✅ | ✅ | ✅ |
| 用户管理 | ✅ | ❌ | ❌ | ❌ |
| 查看分析数据 | ✅ | ❌ | ❌ | ❌ |
| 导出数据 | ✅ | ❌ | ❌ | ❌ |

### 限流配置

| 客户端类型 | 每小时请求数 |
|------------|--------------|
| ADMIN_WEB | 1000 |
| UNIAPP | 500 |
| MOBILE_APP | 300 |
| THIRD_PARTY | 100 |
| UNKNOWN | 50 |

## 中间件使用

### 1. withAuth - 基础认证

```typescript
export async function GET(request: NextRequest) {
  return withAuth(request, async (req, user, session, clientInfo) => {
    // 处理逻辑
  });
}
```

### 2. withAdminAccess - 管理员访问

```typescript
export async function GET(request: NextRequest) {
  return withAdminAccess()(request, async (req, user, session, clientInfo) => {
    // 仅管理员且admin-web客户端可访问
  });
}
```

### 3. withUserAccess - 用户访问

```typescript
export async function GET(request: NextRequest) {
  return withUserAccess()(request, async (req, user, session, clientInfo) => {
    // 所有认证用户可访问（根据客户端类型限制）
  });
}
```

## 客户端配置

### UniApp客户端配置

在UniApp项目的`utils/api.js`中：

```javascript
// 设置客户端标识
const config = {
  headers: {
    'X-Client-Type': 'uniapp',
    'X-Client-Version': '1.0.0',
    'X-Client-Platform': 'android', // 或 'ios'
  }
};
```

### Admin Web客户端配置

在admin-system的`lib/api-client.ts`中：

```typescript
const headers = {
  'X-Client-Type': 'admin-web',
  'X-Client-Version': '1.0.0',
  'X-Client-Platform': 'web',
};
```

## API 响应格式

### 管理端API响应

```json
{
  "success": true,
  "data": {
    "users": [...],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 100,
      "totalPages": 10
    }
  },
  "message": "获取用户列表成功"
}
```

### 移动端API响应

```json
{
  "success": true,
  "data": {
    "user": {
      "id": 1,
      "username": "user1",
      "full_name": "用户1"
    },
    "client_config": {
      "upload_max_size": 10485760,
      "supported_formats": ["jpg", "png", "gif"],
      "api_version": "1.0"
    }
  },
  "message": "登录成功"
}
```

## 安全考虑

### 1. 客户端验证

- 检查客户端类型和版本
- 验证客户端权限
- 记录客户端信息到审计日志

### 2. 数据过滤

- 管理端：返回完整数据
- 移动端：过滤敏感信息
- 第三方：最小化数据返回

### 3. 限流保护

- 基于客户端类型的差异化限流
- IP级别的限流保护
- 用户级别的限流保护

## 迁移指南

### 从通用API迁移到专用API

#### UniApp客户端

**旧方式：**
```javascript
// 使用通用登录接口
const result = await api.post('/auth/login', data);
```

**新方式：**
```javascript
// 使用移动端专用接口
const result = await api.post('/mobile/auth/login', data);
```

#### Admin Web客户端

**旧方式：**
```typescript
// 直接调用API
const response = await fetch('/api/users');
```

**新方式：**
```typescript
// 使用服务层
const result = await UserService.getUsers();
```

## 监控和日志

### 审计日志增强

所有API调用都会记录：
- 客户端类型和版本
- 设备信息（移动端）
- 平台信息
- IP地址和User-Agent

### 监控指标

- 按客户端类型的API调用统计
- 错误率监控
- 性能监控
- 安全事件监控

## 最佳实践

1. **明确客户端标识**：始终在请求头中包含客户端信息
2. **使用专用接口**：避免使用通用接口，选择适合的专用接口
3. **错误处理**：根据客户端类型提供适当的错误信息
4. **版本管理**：通过客户端版本号进行API兼容性管理
5. **安全第一**：最小权限原则，只返回必要的数据

## 故障排除

### 常见错误

1. **403 - 此客户端无法访问管理API**
   - 检查是否使用了正确的客户端标识
   - 确认使用的是移动端专用接口

2. **403 - 不受信任的客户端**
   - 检查X-Client-Type头是否正确设置
   - 确认客户端类型在支持列表中

3. **429 - 请求过于频繁**
   - 检查是否超过了客户端类型的限流限制
   - 实现适当的请求间隔控制
