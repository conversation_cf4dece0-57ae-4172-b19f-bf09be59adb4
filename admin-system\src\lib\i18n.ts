// 国际化配置文件
export const zh_CN = {
  // 通用
  common: {
    loading: '加载中...',
    submit: '提交',
    cancel: '取消',
    confirm: '确认',
    delete: '删除',
    edit: '编辑',
    save: '保存',
    search: '搜索',
    reset: '重置',
    back: '返回',
    next: '下一步',
    previous: '上一步',
    close: '关闭',
    success: '成功',
    error: '错误',
    warning: '警告',
    info: '信息',
  },

  // 认证相关
  auth: {
    // 登录页面
    login: {
      title: '管理系统',
      subtitle: '登录您的账户',
      signIn: '登录',
      signingIn: '登录中...',
      emailOrUsername: '邮箱或用户名',
      emailOrUsernamePlaceholder: '请输入邮箱或用户名',
      password: '密码',
      passwordPlaceholder: '请输入密码',
      noAccount: '还没有账户？',
      signUp: '注册',
      demoCredentials: '演示账户：<EMAIL> / password123',
      loginFailed: '登录失败',
      unexpectedError: '发生意外错误',
    },

    // 注册页面
    register: {
      title: '管理系统',
      subtitle: '创建您的账户',
      signUp: '注册',
      creatingAccount: '创建账户中...',
      createAccount: '创建账户',
      username: '用户名',
      usernamePlaceholder: '选择一个用户名',
      email: '邮箱',
      emailPlaceholder: '请输入邮箱',
      fullName: '姓名（可选）',
      fullNamePlaceholder: '请输入您的姓名',
      confirmPassword: '确认密码',
      confirmPasswordPlaceholder: '请确认密码',
      fillInformation: '填写以下信息创建您的账户',
      alreadyHaveAccount: '已有账户？',
      signIn: '登录',
      
      // 验证错误
      validation: {
        usernameRequired: '用户名是必填项',
        usernameMinLength: '用户名至少需要3个字符',
        usernameInvalid: '用户名只能包含字母、数字和下划线',
        emailRequired: '邮箱是必填项',
        emailInvalid: '请输入有效的邮箱地址',
        passwordRequired: '密码是必填项',
        passwordMinLength: '密码至少需要8个字符',
        passwordComplexity: '密码必须包含至少一个小写字母、一个大写字母和一个数字',
        confirmPasswordRequired: '请确认密码',
        passwordMismatch: '密码不匹配',
      },
    },
  },

  // 仪表板
  dashboard: {
    title: '管理仪表板',
    welcome: '欢迎回来',
    welcomeSubtitle: '这是您系统今天的情况',
    
    // 统计卡片
    stats: {
      totalUsers: '总用户数',
      activeUsers: '活跃用户',
      activeSessions: '活跃会话',
      systemHealth: '系统健康',
      fromLastMonth: '较上月',
      currentlyOnline: '当前在线',
      fromYesterday: '较昨日',
      allSystemsOperational: '所有系统正常运行',
      good: '良好',
      warning: '警告',
      critical: '严重',
    },

    // 快速操作
    quickActions: {
      userManagement: '用户管理',
      userManagementDesc: '管理用户账户、角色和权限',
      viewAllUsers: '查看所有用户',
      manageRoles: '管理角色',
      analytics: '分析',
      analyticsDesc: '查看系统分析和报告',
      usageReports: '使用报告',
      activityLogs: '活动日志',
    },

    // 最近活动
    recentActivity: {
      title: '最近活动',
      subtitle: '最新的系统事件和用户操作',
      userLoggedIn: '用户 {username} 已登录',
      newUserRegistered: '新用户注册：{username}',
      systemBackupCompleted: '系统备份完成',
      minutesAgo: '{minutes} 分钟前',
      hoursAgo: '{hours} 小时前',
    },
  },

  // 错误消息
  errors: {
    networkError: '网络错误，请重试',
    serverError: '服务器错误',
    unauthorized: '未授权访问',
    forbidden: '权限不足',
    notFound: '未找到',
    validationFailed: '验证失败',
    tooManyAttempts: '尝试次数过多，请稍后再试',
  },

  // 成功消息
  success: {
    loginSuccess: '登录成功',
    registerSuccess: '注册成功',
    updateSuccess: '更新成功',
    deleteSuccess: '删除成功',
    saveSuccess: '保存成功',
  },
};

export type I18nKeys = typeof zh_CN;

// 简单的国际化函数
export function t(key: string, params?: Record<string, string | number>): string {
  const keys = key.split('.');
  let value: any = zh_CN;
  
  for (const k of keys) {
    value = value?.[k];
  }
  
  if (typeof value !== 'string') {
    return key; // 如果找不到翻译，返回原始key
  }
  
  // 替换参数
  if (params) {
    return value.replace(/\{(\w+)\}/g, (match: string, paramKey: string) => {
      return params[paramKey]?.toString() || match;
    });
  }
  
  return value;
}
