/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/lru-cache@7.18.3";
exports.ids = ["vendor-chunks/lru-cache@7.18.3"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/lru-cache@7.18.3/node_modules/lru-cache/index.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/.pnpm/lru-cache@7.18.3/node_modules/lru-cache/index.js ***!
  \*****************************************************************************/
/***/ ((module) => {

eval("const perf =\n  typeof performance === 'object' &&\n  performance &&\n  typeof performance.now === 'function'\n    ? performance\n    : Date\n\nconst hasAbortController = typeof AbortController === 'function'\n\n// minimal backwards-compatibility polyfill\n// this doesn't have nearly all the checks and whatnot that\n// actual AbortController/Signal has, but it's enough for\n// our purposes, and if used properly, behaves the same.\nconst AC = hasAbortController\n  ? AbortController\n  : class AbortController {\n      constructor() {\n        this.signal = new AS()\n      }\n      abort(reason = new Error('This operation was aborted')) {\n        this.signal.reason = this.signal.reason || reason\n        this.signal.aborted = true\n        this.signal.dispatchEvent({\n          type: 'abort',\n          target: this.signal,\n        })\n      }\n    }\n\nconst hasAbortSignal = typeof AbortSignal === 'function'\n// Some polyfills put this on the AC class, not global\nconst hasACAbortSignal = typeof AC.AbortSignal === 'function'\nconst AS = hasAbortSignal\n  ? AbortSignal\n  : hasACAbortSignal\n  ? AC.AbortController\n  : class AbortSignal {\n      constructor() {\n        this.reason = undefined\n        this.aborted = false\n        this._listeners = []\n      }\n      dispatchEvent(e) {\n        if (e.type === 'abort') {\n          this.aborted = true\n          this.onabort(e)\n          this._listeners.forEach(f => f(e), this)\n        }\n      }\n      onabort() {}\n      addEventListener(ev, fn) {\n        if (ev === 'abort') {\n          this._listeners.push(fn)\n        }\n      }\n      removeEventListener(ev, fn) {\n        if (ev === 'abort') {\n          this._listeners = this._listeners.filter(f => f !== fn)\n        }\n      }\n    }\n\nconst warned = new Set()\nconst deprecatedOption = (opt, instead) => {\n  const code = `LRU_CACHE_OPTION_${opt}`\n  if (shouldWarn(code)) {\n    warn(code, `${opt} option`, `options.${instead}`, LRUCache)\n  }\n}\nconst deprecatedMethod = (method, instead) => {\n  const code = `LRU_CACHE_METHOD_${method}`\n  if (shouldWarn(code)) {\n    const { prototype } = LRUCache\n    const { get } = Object.getOwnPropertyDescriptor(prototype, method)\n    warn(code, `${method} method`, `cache.${instead}()`, get)\n  }\n}\nconst deprecatedProperty = (field, instead) => {\n  const code = `LRU_CACHE_PROPERTY_${field}`\n  if (shouldWarn(code)) {\n    const { prototype } = LRUCache\n    const { get } = Object.getOwnPropertyDescriptor(prototype, field)\n    warn(code, `${field} property`, `cache.${instead}`, get)\n  }\n}\n\nconst emitWarning = (...a) => {\n  typeof process === 'object' &&\n  process &&\n  typeof process.emitWarning === 'function'\n    ? process.emitWarning(...a)\n    : console.error(...a)\n}\n\nconst shouldWarn = code => !warned.has(code)\n\nconst warn = (code, what, instead, fn) => {\n  warned.add(code)\n  const msg = `The ${what} is deprecated. Please use ${instead} instead.`\n  emitWarning(msg, 'DeprecationWarning', code, fn)\n}\n\nconst isPosInt = n => n && n === Math.floor(n) && n > 0 && isFinite(n)\n\n/* istanbul ignore next - This is a little bit ridiculous, tbh.\n * The maximum array length is 2^32-1 or thereabouts on most JS impls.\n * And well before that point, you're caching the entire world, I mean,\n * that's ~32GB of just integers for the next/prev links, plus whatever\n * else to hold that many keys and values.  Just filling the memory with\n * zeroes at init time is brutal when you get that big.\n * But why not be complete?\n * Maybe in the future, these limits will have expanded. */\nconst getUintArray = max =>\n  !isPosInt(max)\n    ? null\n    : max <= Math.pow(2, 8)\n    ? Uint8Array\n    : max <= Math.pow(2, 16)\n    ? Uint16Array\n    : max <= Math.pow(2, 32)\n    ? Uint32Array\n    : max <= Number.MAX_SAFE_INTEGER\n    ? ZeroArray\n    : null\n\nclass ZeroArray extends Array {\n  constructor(size) {\n    super(size)\n    this.fill(0)\n  }\n}\n\nclass Stack {\n  constructor(max) {\n    if (max === 0) {\n      return []\n    }\n    const UintArray = getUintArray(max)\n    this.heap = new UintArray(max)\n    this.length = 0\n  }\n  push(n) {\n    this.heap[this.length++] = n\n  }\n  pop() {\n    return this.heap[--this.length]\n  }\n}\n\nclass LRUCache {\n  constructor(options = {}) {\n    const {\n      max = 0,\n      ttl,\n      ttlResolution = 1,\n      ttlAutopurge,\n      updateAgeOnGet,\n      updateAgeOnHas,\n      allowStale,\n      dispose,\n      disposeAfter,\n      noDisposeOnSet,\n      noUpdateTTL,\n      maxSize = 0,\n      maxEntrySize = 0,\n      sizeCalculation,\n      fetchMethod,\n      fetchContext,\n      noDeleteOnFetchRejection,\n      noDeleteOnStaleGet,\n      allowStaleOnFetchRejection,\n      allowStaleOnFetchAbort,\n      ignoreFetchAbort,\n    } = options\n\n    // deprecated options, don't trigger a warning for getting them if\n    // the thing being passed in is another LRUCache we're copying.\n    const { length, maxAge, stale } =\n      options instanceof LRUCache ? {} : options\n\n    if (max !== 0 && !isPosInt(max)) {\n      throw new TypeError('max option must be a nonnegative integer')\n    }\n\n    const UintArray = max ? getUintArray(max) : Array\n    if (!UintArray) {\n      throw new Error('invalid max value: ' + max)\n    }\n\n    this.max = max\n    this.maxSize = maxSize\n    this.maxEntrySize = maxEntrySize || this.maxSize\n    this.sizeCalculation = sizeCalculation || length\n    if (this.sizeCalculation) {\n      if (!this.maxSize && !this.maxEntrySize) {\n        throw new TypeError(\n          'cannot set sizeCalculation without setting maxSize or maxEntrySize'\n        )\n      }\n      if (typeof this.sizeCalculation !== 'function') {\n        throw new TypeError('sizeCalculation set to non-function')\n      }\n    }\n\n    this.fetchMethod = fetchMethod || null\n    if (this.fetchMethod && typeof this.fetchMethod !== 'function') {\n      throw new TypeError(\n        'fetchMethod must be a function if specified'\n      )\n    }\n\n    this.fetchContext = fetchContext\n    if (!this.fetchMethod && fetchContext !== undefined) {\n      throw new TypeError(\n        'cannot set fetchContext without fetchMethod'\n      )\n    }\n\n    this.keyMap = new Map()\n    this.keyList = new Array(max).fill(null)\n    this.valList = new Array(max).fill(null)\n    this.next = new UintArray(max)\n    this.prev = new UintArray(max)\n    this.head = 0\n    this.tail = 0\n    this.free = new Stack(max)\n    this.initialFill = 1\n    this.size = 0\n\n    if (typeof dispose === 'function') {\n      this.dispose = dispose\n    }\n    if (typeof disposeAfter === 'function') {\n      this.disposeAfter = disposeAfter\n      this.disposed = []\n    } else {\n      this.disposeAfter = null\n      this.disposed = null\n    }\n    this.noDisposeOnSet = !!noDisposeOnSet\n    this.noUpdateTTL = !!noUpdateTTL\n    this.noDeleteOnFetchRejection = !!noDeleteOnFetchRejection\n    this.allowStaleOnFetchRejection = !!allowStaleOnFetchRejection\n    this.allowStaleOnFetchAbort = !!allowStaleOnFetchAbort\n    this.ignoreFetchAbort = !!ignoreFetchAbort\n\n    // NB: maxEntrySize is set to maxSize if it's set\n    if (this.maxEntrySize !== 0) {\n      if (this.maxSize !== 0) {\n        if (!isPosInt(this.maxSize)) {\n          throw new TypeError(\n            'maxSize must be a positive integer if specified'\n          )\n        }\n      }\n      if (!isPosInt(this.maxEntrySize)) {\n        throw new TypeError(\n          'maxEntrySize must be a positive integer if specified'\n        )\n      }\n      this.initializeSizeTracking()\n    }\n\n    this.allowStale = !!allowStale || !!stale\n    this.noDeleteOnStaleGet = !!noDeleteOnStaleGet\n    this.updateAgeOnGet = !!updateAgeOnGet\n    this.updateAgeOnHas = !!updateAgeOnHas\n    this.ttlResolution =\n      isPosInt(ttlResolution) || ttlResolution === 0\n        ? ttlResolution\n        : 1\n    this.ttlAutopurge = !!ttlAutopurge\n    this.ttl = ttl || maxAge || 0\n    if (this.ttl) {\n      if (!isPosInt(this.ttl)) {\n        throw new TypeError(\n          'ttl must be a positive integer if specified'\n        )\n      }\n      this.initializeTTLTracking()\n    }\n\n    // do not allow completely unbounded caches\n    if (this.max === 0 && this.ttl === 0 && this.maxSize === 0) {\n      throw new TypeError(\n        'At least one of max, maxSize, or ttl is required'\n      )\n    }\n    if (!this.ttlAutopurge && !this.max && !this.maxSize) {\n      const code = 'LRU_CACHE_UNBOUNDED'\n      if (shouldWarn(code)) {\n        warned.add(code)\n        const msg =\n          'TTL caching without ttlAutopurge, max, or maxSize can ' +\n          'result in unbounded memory consumption.'\n        emitWarning(msg, 'UnboundedCacheWarning', code, LRUCache)\n      }\n    }\n\n    if (stale) {\n      deprecatedOption('stale', 'allowStale')\n    }\n    if (maxAge) {\n      deprecatedOption('maxAge', 'ttl')\n    }\n    if (length) {\n      deprecatedOption('length', 'sizeCalculation')\n    }\n  }\n\n  getRemainingTTL(key) {\n    return this.has(key, { updateAgeOnHas: false }) ? Infinity : 0\n  }\n\n  initializeTTLTracking() {\n    this.ttls = new ZeroArray(this.max)\n    this.starts = new ZeroArray(this.max)\n\n    this.setItemTTL = (index, ttl, start = perf.now()) => {\n      this.starts[index] = ttl !== 0 ? start : 0\n      this.ttls[index] = ttl\n      if (ttl !== 0 && this.ttlAutopurge) {\n        const t = setTimeout(() => {\n          if (this.isStale(index)) {\n            this.delete(this.keyList[index])\n          }\n        }, ttl + 1)\n        /* istanbul ignore else - unref() not supported on all platforms */\n        if (t.unref) {\n          t.unref()\n        }\n      }\n    }\n\n    this.updateItemAge = index => {\n      this.starts[index] = this.ttls[index] !== 0 ? perf.now() : 0\n    }\n\n    this.statusTTL = (status, index) => {\n      if (status) {\n        status.ttl = this.ttls[index]\n        status.start = this.starts[index]\n        status.now = cachedNow || getNow()\n        status.remainingTTL = status.now + status.ttl - status.start\n      }\n    }\n\n    // debounce calls to perf.now() to 1s so we're not hitting\n    // that costly call repeatedly.\n    let cachedNow = 0\n    const getNow = () => {\n      const n = perf.now()\n      if (this.ttlResolution > 0) {\n        cachedNow = n\n        const t = setTimeout(\n          () => (cachedNow = 0),\n          this.ttlResolution\n        )\n        /* istanbul ignore else - not available on all platforms */\n        if (t.unref) {\n          t.unref()\n        }\n      }\n      return n\n    }\n\n    this.getRemainingTTL = key => {\n      const index = this.keyMap.get(key)\n      if (index === undefined) {\n        return 0\n      }\n      return this.ttls[index] === 0 || this.starts[index] === 0\n        ? Infinity\n        : this.starts[index] +\n            this.ttls[index] -\n            (cachedNow || getNow())\n    }\n\n    this.isStale = index => {\n      return (\n        this.ttls[index] !== 0 &&\n        this.starts[index] !== 0 &&\n        (cachedNow || getNow()) - this.starts[index] >\n          this.ttls[index]\n      )\n    }\n  }\n  updateItemAge(_index) {}\n  statusTTL(_status, _index) {}\n  setItemTTL(_index, _ttl, _start) {}\n  isStale(_index) {\n    return false\n  }\n\n  initializeSizeTracking() {\n    this.calculatedSize = 0\n    this.sizes = new ZeroArray(this.max)\n    this.removeItemSize = index => {\n      this.calculatedSize -= this.sizes[index]\n      this.sizes[index] = 0\n    }\n    this.requireSize = (k, v, size, sizeCalculation) => {\n      // provisionally accept background fetches.\n      // actual value size will be checked when they return.\n      if (this.isBackgroundFetch(v)) {\n        return 0\n      }\n      if (!isPosInt(size)) {\n        if (sizeCalculation) {\n          if (typeof sizeCalculation !== 'function') {\n            throw new TypeError('sizeCalculation must be a function')\n          }\n          size = sizeCalculation(v, k)\n          if (!isPosInt(size)) {\n            throw new TypeError(\n              'sizeCalculation return invalid (expect positive integer)'\n            )\n          }\n        } else {\n          throw new TypeError(\n            'invalid size value (must be positive integer). ' +\n              'When maxSize or maxEntrySize is used, sizeCalculation or size ' +\n              'must be set.'\n          )\n        }\n      }\n      return size\n    }\n    this.addItemSize = (index, size, status) => {\n      this.sizes[index] = size\n      if (this.maxSize) {\n        const maxSize = this.maxSize - this.sizes[index]\n        while (this.calculatedSize > maxSize) {\n          this.evict(true)\n        }\n      }\n      this.calculatedSize += this.sizes[index]\n      if (status) {\n        status.entrySize = size\n        status.totalCalculatedSize = this.calculatedSize\n      }\n    }\n  }\n  removeItemSize(_index) {}\n  addItemSize(_index, _size) {}\n  requireSize(_k, _v, size, sizeCalculation) {\n    if (size || sizeCalculation) {\n      throw new TypeError(\n        'cannot set size without setting maxSize or maxEntrySize on cache'\n      )\n    }\n  }\n\n  *indexes({ allowStale = this.allowStale } = {}) {\n    if (this.size) {\n      for (let i = this.tail; true; ) {\n        if (!this.isValidIndex(i)) {\n          break\n        }\n        if (allowStale || !this.isStale(i)) {\n          yield i\n        }\n        if (i === this.head) {\n          break\n        } else {\n          i = this.prev[i]\n        }\n      }\n    }\n  }\n\n  *rindexes({ allowStale = this.allowStale } = {}) {\n    if (this.size) {\n      for (let i = this.head; true; ) {\n        if (!this.isValidIndex(i)) {\n          break\n        }\n        if (allowStale || !this.isStale(i)) {\n          yield i\n        }\n        if (i === this.tail) {\n          break\n        } else {\n          i = this.next[i]\n        }\n      }\n    }\n  }\n\n  isValidIndex(index) {\n    return (\n      index !== undefined &&\n      this.keyMap.get(this.keyList[index]) === index\n    )\n  }\n\n  *entries() {\n    for (const i of this.indexes()) {\n      if (\n        this.valList[i] !== undefined &&\n        this.keyList[i] !== undefined &&\n        !this.isBackgroundFetch(this.valList[i])\n      ) {\n        yield [this.keyList[i], this.valList[i]]\n      }\n    }\n  }\n  *rentries() {\n    for (const i of this.rindexes()) {\n      if (\n        this.valList[i] !== undefined &&\n        this.keyList[i] !== undefined &&\n        !this.isBackgroundFetch(this.valList[i])\n      ) {\n        yield [this.keyList[i], this.valList[i]]\n      }\n    }\n  }\n\n  *keys() {\n    for (const i of this.indexes()) {\n      if (\n        this.keyList[i] !== undefined &&\n        !this.isBackgroundFetch(this.valList[i])\n      ) {\n        yield this.keyList[i]\n      }\n    }\n  }\n  *rkeys() {\n    for (const i of this.rindexes()) {\n      if (\n        this.keyList[i] !== undefined &&\n        !this.isBackgroundFetch(this.valList[i])\n      ) {\n        yield this.keyList[i]\n      }\n    }\n  }\n\n  *values() {\n    for (const i of this.indexes()) {\n      if (\n        this.valList[i] !== undefined &&\n        !this.isBackgroundFetch(this.valList[i])\n      ) {\n        yield this.valList[i]\n      }\n    }\n  }\n  *rvalues() {\n    for (const i of this.rindexes()) {\n      if (\n        this.valList[i] !== undefined &&\n        !this.isBackgroundFetch(this.valList[i])\n      ) {\n        yield this.valList[i]\n      }\n    }\n  }\n\n  [Symbol.iterator]() {\n    return this.entries()\n  }\n\n  find(fn, getOptions) {\n    for (const i of this.indexes()) {\n      const v = this.valList[i]\n      const value = this.isBackgroundFetch(v)\n        ? v.__staleWhileFetching\n        : v\n      if (value === undefined) continue\n      if (fn(value, this.keyList[i], this)) {\n        return this.get(this.keyList[i], getOptions)\n      }\n    }\n  }\n\n  forEach(fn, thisp = this) {\n    for (const i of this.indexes()) {\n      const v = this.valList[i]\n      const value = this.isBackgroundFetch(v)\n        ? v.__staleWhileFetching\n        : v\n      if (value === undefined) continue\n      fn.call(thisp, value, this.keyList[i], this)\n    }\n  }\n\n  rforEach(fn, thisp = this) {\n    for (const i of this.rindexes()) {\n      const v = this.valList[i]\n      const value = this.isBackgroundFetch(v)\n        ? v.__staleWhileFetching\n        : v\n      if (value === undefined) continue\n      fn.call(thisp, value, this.keyList[i], this)\n    }\n  }\n\n  get prune() {\n    deprecatedMethod('prune', 'purgeStale')\n    return this.purgeStale\n  }\n\n  purgeStale() {\n    let deleted = false\n    for (const i of this.rindexes({ allowStale: true })) {\n      if (this.isStale(i)) {\n        this.delete(this.keyList[i])\n        deleted = true\n      }\n    }\n    return deleted\n  }\n\n  dump() {\n    const arr = []\n    for (const i of this.indexes({ allowStale: true })) {\n      const key = this.keyList[i]\n      const v = this.valList[i]\n      const value = this.isBackgroundFetch(v)\n        ? v.__staleWhileFetching\n        : v\n      if (value === undefined) continue\n      const entry = { value }\n      if (this.ttls) {\n        entry.ttl = this.ttls[i]\n        // always dump the start relative to a portable timestamp\n        // it's ok for this to be a bit slow, it's a rare operation.\n        const age = perf.now() - this.starts[i]\n        entry.start = Math.floor(Date.now() - age)\n      }\n      if (this.sizes) {\n        entry.size = this.sizes[i]\n      }\n      arr.unshift([key, entry])\n    }\n    return arr\n  }\n\n  load(arr) {\n    this.clear()\n    for (const [key, entry] of arr) {\n      if (entry.start) {\n        // entry.start is a portable timestamp, but we may be using\n        // node's performance.now(), so calculate the offset.\n        // it's ok for this to be a bit slow, it's a rare operation.\n        const age = Date.now() - entry.start\n        entry.start = perf.now() - age\n      }\n      this.set(key, entry.value, entry)\n    }\n  }\n\n  dispose(_v, _k, _reason) {}\n\n  set(\n    k,\n    v,\n    {\n      ttl = this.ttl,\n      start,\n      noDisposeOnSet = this.noDisposeOnSet,\n      size = 0,\n      sizeCalculation = this.sizeCalculation,\n      noUpdateTTL = this.noUpdateTTL,\n      status,\n    } = {}\n  ) {\n    size = this.requireSize(k, v, size, sizeCalculation)\n    // if the item doesn't fit, don't do anything\n    // NB: maxEntrySize set to maxSize by default\n    if (this.maxEntrySize && size > this.maxEntrySize) {\n      if (status) {\n        status.set = 'miss'\n        status.maxEntrySizeExceeded = true\n      }\n      // have to delete, in case a background fetch is there already.\n      // in non-async cases, this is a no-op\n      this.delete(k)\n      return this\n    }\n    let index = this.size === 0 ? undefined : this.keyMap.get(k)\n    if (index === undefined) {\n      // addition\n      index = this.newIndex()\n      this.keyList[index] = k\n      this.valList[index] = v\n      this.keyMap.set(k, index)\n      this.next[this.tail] = index\n      this.prev[index] = this.tail\n      this.tail = index\n      this.size++\n      this.addItemSize(index, size, status)\n      if (status) {\n        status.set = 'add'\n      }\n      noUpdateTTL = false\n    } else {\n      // update\n      this.moveToTail(index)\n      const oldVal = this.valList[index]\n      if (v !== oldVal) {\n        if (this.isBackgroundFetch(oldVal)) {\n          oldVal.__abortController.abort(new Error('replaced'))\n        } else {\n          if (!noDisposeOnSet) {\n            this.dispose(oldVal, k, 'set')\n            if (this.disposeAfter) {\n              this.disposed.push([oldVal, k, 'set'])\n            }\n          }\n        }\n        this.removeItemSize(index)\n        this.valList[index] = v\n        this.addItemSize(index, size, status)\n        if (status) {\n          status.set = 'replace'\n          const oldValue =\n            oldVal && this.isBackgroundFetch(oldVal)\n              ? oldVal.__staleWhileFetching\n              : oldVal\n          if (oldValue !== undefined) status.oldValue = oldValue\n        }\n      } else if (status) {\n        status.set = 'update'\n      }\n    }\n    if (ttl !== 0 && this.ttl === 0 && !this.ttls) {\n      this.initializeTTLTracking()\n    }\n    if (!noUpdateTTL) {\n      this.setItemTTL(index, ttl, start)\n    }\n    this.statusTTL(status, index)\n    if (this.disposeAfter) {\n      while (this.disposed.length) {\n        this.disposeAfter(...this.disposed.shift())\n      }\n    }\n    return this\n  }\n\n  newIndex() {\n    if (this.size === 0) {\n      return this.tail\n    }\n    if (this.size === this.max && this.max !== 0) {\n      return this.evict(false)\n    }\n    if (this.free.length !== 0) {\n      return this.free.pop()\n    }\n    // initial fill, just keep writing down the list\n    return this.initialFill++\n  }\n\n  pop() {\n    if (this.size) {\n      const val = this.valList[this.head]\n      this.evict(true)\n      return val\n    }\n  }\n\n  evict(free) {\n    const head = this.head\n    const k = this.keyList[head]\n    const v = this.valList[head]\n    if (this.isBackgroundFetch(v)) {\n      v.__abortController.abort(new Error('evicted'))\n    } else {\n      this.dispose(v, k, 'evict')\n      if (this.disposeAfter) {\n        this.disposed.push([v, k, 'evict'])\n      }\n    }\n    this.removeItemSize(head)\n    // if we aren't about to use the index, then null these out\n    if (free) {\n      this.keyList[head] = null\n      this.valList[head] = null\n      this.free.push(head)\n    }\n    this.head = this.next[head]\n    this.keyMap.delete(k)\n    this.size--\n    return head\n  }\n\n  has(k, { updateAgeOnHas = this.updateAgeOnHas, status } = {}) {\n    const index = this.keyMap.get(k)\n    if (index !== undefined) {\n      if (!this.isStale(index)) {\n        if (updateAgeOnHas) {\n          this.updateItemAge(index)\n        }\n        if (status) status.has = 'hit'\n        this.statusTTL(status, index)\n        return true\n      } else if (status) {\n        status.has = 'stale'\n        this.statusTTL(status, index)\n      }\n    } else if (status) {\n      status.has = 'miss'\n    }\n    return false\n  }\n\n  // like get(), but without any LRU updating or TTL expiration\n  peek(k, { allowStale = this.allowStale } = {}) {\n    const index = this.keyMap.get(k)\n    if (index !== undefined && (allowStale || !this.isStale(index))) {\n      const v = this.valList[index]\n      // either stale and allowed, or forcing a refresh of non-stale value\n      return this.isBackgroundFetch(v) ? v.__staleWhileFetching : v\n    }\n  }\n\n  backgroundFetch(k, index, options, context) {\n    const v = index === undefined ? undefined : this.valList[index]\n    if (this.isBackgroundFetch(v)) {\n      return v\n    }\n    const ac = new AC()\n    if (options.signal) {\n      options.signal.addEventListener('abort', () =>\n        ac.abort(options.signal.reason)\n      )\n    }\n    const fetchOpts = {\n      signal: ac.signal,\n      options,\n      context,\n    }\n    const cb = (v, updateCache = false) => {\n      const { aborted } = ac.signal\n      const ignoreAbort = options.ignoreFetchAbort && v !== undefined\n      if (options.status) {\n        if (aborted && !updateCache) {\n          options.status.fetchAborted = true\n          options.status.fetchError = ac.signal.reason\n          if (ignoreAbort) options.status.fetchAbortIgnored = true\n        } else {\n          options.status.fetchResolved = true\n        }\n      }\n      if (aborted && !ignoreAbort && !updateCache) {\n        return fetchFail(ac.signal.reason)\n      }\n      // either we didn't abort, and are still here, or we did, and ignored\n      if (this.valList[index] === p) {\n        if (v === undefined) {\n          if (p.__staleWhileFetching) {\n            this.valList[index] = p.__staleWhileFetching\n          } else {\n            this.delete(k)\n          }\n        } else {\n          if (options.status) options.status.fetchUpdated = true\n          this.set(k, v, fetchOpts.options)\n        }\n      }\n      return v\n    }\n    const eb = er => {\n      if (options.status) {\n        options.status.fetchRejected = true\n        options.status.fetchError = er\n      }\n      return fetchFail(er)\n    }\n    const fetchFail = er => {\n      const { aborted } = ac.signal\n      const allowStaleAborted =\n        aborted && options.allowStaleOnFetchAbort\n      const allowStale =\n        allowStaleAborted || options.allowStaleOnFetchRejection\n      const noDelete = allowStale || options.noDeleteOnFetchRejection\n      if (this.valList[index] === p) {\n        // if we allow stale on fetch rejections, then we need to ensure that\n        // the stale value is not removed from the cache when the fetch fails.\n        const del = !noDelete || p.__staleWhileFetching === undefined\n        if (del) {\n          this.delete(k)\n        } else if (!allowStaleAborted) {\n          // still replace the *promise* with the stale value,\n          // since we are done with the promise at this point.\n          // leave it untouched if we're still waiting for an\n          // aborted background fetch that hasn't yet returned.\n          this.valList[index] = p.__staleWhileFetching\n        }\n      }\n      if (allowStale) {\n        if (options.status && p.__staleWhileFetching !== undefined) {\n          options.status.returnedStale = true\n        }\n        return p.__staleWhileFetching\n      } else if (p.__returned === p) {\n        throw er\n      }\n    }\n    const pcall = (res, rej) => {\n      this.fetchMethod(k, v, fetchOpts).then(v => res(v), rej)\n      // ignored, we go until we finish, regardless.\n      // defer check until we are actually aborting,\n      // so fetchMethod can override.\n      ac.signal.addEventListener('abort', () => {\n        if (\n          !options.ignoreFetchAbort ||\n          options.allowStaleOnFetchAbort\n        ) {\n          res()\n          // when it eventually resolves, update the cache.\n          if (options.allowStaleOnFetchAbort) {\n            res = v => cb(v, true)\n          }\n        }\n      })\n    }\n    if (options.status) options.status.fetchDispatched = true\n    const p = new Promise(pcall).then(cb, eb)\n    p.__abortController = ac\n    p.__staleWhileFetching = v\n    p.__returned = null\n    if (index === undefined) {\n      // internal, don't expose status.\n      this.set(k, p, { ...fetchOpts.options, status: undefined })\n      index = this.keyMap.get(k)\n    } else {\n      this.valList[index] = p\n    }\n    return p\n  }\n\n  isBackgroundFetch(p) {\n    return (\n      p &&\n      typeof p === 'object' &&\n      typeof p.then === 'function' &&\n      Object.prototype.hasOwnProperty.call(\n        p,\n        '__staleWhileFetching'\n      ) &&\n      Object.prototype.hasOwnProperty.call(p, '__returned') &&\n      (p.__returned === p || p.__returned === null)\n    )\n  }\n\n  // this takes the union of get() and set() opts, because it does both\n  async fetch(\n    k,\n    {\n      // get options\n      allowStale = this.allowStale,\n      updateAgeOnGet = this.updateAgeOnGet,\n      noDeleteOnStaleGet = this.noDeleteOnStaleGet,\n      // set options\n      ttl = this.ttl,\n      noDisposeOnSet = this.noDisposeOnSet,\n      size = 0,\n      sizeCalculation = this.sizeCalculation,\n      noUpdateTTL = this.noUpdateTTL,\n      // fetch exclusive options\n      noDeleteOnFetchRejection = this.noDeleteOnFetchRejection,\n      allowStaleOnFetchRejection = this.allowStaleOnFetchRejection,\n      ignoreFetchAbort = this.ignoreFetchAbort,\n      allowStaleOnFetchAbort = this.allowStaleOnFetchAbort,\n      fetchContext = this.fetchContext,\n      forceRefresh = false,\n      status,\n      signal,\n    } = {}\n  ) {\n    if (!this.fetchMethod) {\n      if (status) status.fetch = 'get'\n      return this.get(k, {\n        allowStale,\n        updateAgeOnGet,\n        noDeleteOnStaleGet,\n        status,\n      })\n    }\n\n    const options = {\n      allowStale,\n      updateAgeOnGet,\n      noDeleteOnStaleGet,\n      ttl,\n      noDisposeOnSet,\n      size,\n      sizeCalculation,\n      noUpdateTTL,\n      noDeleteOnFetchRejection,\n      allowStaleOnFetchRejection,\n      allowStaleOnFetchAbort,\n      ignoreFetchAbort,\n      status,\n      signal,\n    }\n\n    let index = this.keyMap.get(k)\n    if (index === undefined) {\n      if (status) status.fetch = 'miss'\n      const p = this.backgroundFetch(k, index, options, fetchContext)\n      return (p.__returned = p)\n    } else {\n      // in cache, maybe already fetching\n      const v = this.valList[index]\n      if (this.isBackgroundFetch(v)) {\n        const stale =\n          allowStale && v.__staleWhileFetching !== undefined\n        if (status) {\n          status.fetch = 'inflight'\n          if (stale) status.returnedStale = true\n        }\n        return stale ? v.__staleWhileFetching : (v.__returned = v)\n      }\n\n      // if we force a refresh, that means do NOT serve the cached value,\n      // unless we are already in the process of refreshing the cache.\n      const isStale = this.isStale(index)\n      if (!forceRefresh && !isStale) {\n        if (status) status.fetch = 'hit'\n        this.moveToTail(index)\n        if (updateAgeOnGet) {\n          this.updateItemAge(index)\n        }\n        this.statusTTL(status, index)\n        return v\n      }\n\n      // ok, it is stale or a forced refresh, and not already fetching.\n      // refresh the cache.\n      const p = this.backgroundFetch(k, index, options, fetchContext)\n      const hasStale = p.__staleWhileFetching !== undefined\n      const staleVal = hasStale && allowStale\n      if (status) {\n        status.fetch = hasStale && isStale ? 'stale' : 'refresh'\n        if (staleVal && isStale) status.returnedStale = true\n      }\n      return staleVal ? p.__staleWhileFetching : (p.__returned = p)\n    }\n  }\n\n  get(\n    k,\n    {\n      allowStale = this.allowStale,\n      updateAgeOnGet = this.updateAgeOnGet,\n      noDeleteOnStaleGet = this.noDeleteOnStaleGet,\n      status,\n    } = {}\n  ) {\n    const index = this.keyMap.get(k)\n    if (index !== undefined) {\n      const value = this.valList[index]\n      const fetching = this.isBackgroundFetch(value)\n      this.statusTTL(status, index)\n      if (this.isStale(index)) {\n        if (status) status.get = 'stale'\n        // delete only if not an in-flight background fetch\n        if (!fetching) {\n          if (!noDeleteOnStaleGet) {\n            this.delete(k)\n          }\n          if (status) status.returnedStale = allowStale\n          return allowStale ? value : undefined\n        } else {\n          if (status) {\n            status.returnedStale =\n              allowStale && value.__staleWhileFetching !== undefined\n          }\n          return allowStale ? value.__staleWhileFetching : undefined\n        }\n      } else {\n        if (status) status.get = 'hit'\n        // if we're currently fetching it, we don't actually have it yet\n        // it's not stale, which means this isn't a staleWhileRefetching.\n        // If it's not stale, and fetching, AND has a __staleWhileFetching\n        // value, then that means the user fetched with {forceRefresh:true},\n        // so it's safe to return that value.\n        if (fetching) {\n          return value.__staleWhileFetching\n        }\n        this.moveToTail(index)\n        if (updateAgeOnGet) {\n          this.updateItemAge(index)\n        }\n        return value\n      }\n    } else if (status) {\n      status.get = 'miss'\n    }\n  }\n\n  connect(p, n) {\n    this.prev[n] = p\n    this.next[p] = n\n  }\n\n  moveToTail(index) {\n    // if tail already, nothing to do\n    // if head, move head to next[index]\n    // else\n    //   move next[prev[index]] to next[index] (head has no prev)\n    //   move prev[next[index]] to prev[index]\n    // prev[index] = tail\n    // next[tail] = index\n    // tail = index\n    if (index !== this.tail) {\n      if (index === this.head) {\n        this.head = this.next[index]\n      } else {\n        this.connect(this.prev[index], this.next[index])\n      }\n      this.connect(this.tail, index)\n      this.tail = index\n    }\n  }\n\n  get del() {\n    deprecatedMethod('del', 'delete')\n    return this.delete\n  }\n\n  delete(k) {\n    let deleted = false\n    if (this.size !== 0) {\n      const index = this.keyMap.get(k)\n      if (index !== undefined) {\n        deleted = true\n        if (this.size === 1) {\n          this.clear()\n        } else {\n          this.removeItemSize(index)\n          const v = this.valList[index]\n          if (this.isBackgroundFetch(v)) {\n            v.__abortController.abort(new Error('deleted'))\n          } else {\n            this.dispose(v, k, 'delete')\n            if (this.disposeAfter) {\n              this.disposed.push([v, k, 'delete'])\n            }\n          }\n          this.keyMap.delete(k)\n          this.keyList[index] = null\n          this.valList[index] = null\n          if (index === this.tail) {\n            this.tail = this.prev[index]\n          } else if (index === this.head) {\n            this.head = this.next[index]\n          } else {\n            this.next[this.prev[index]] = this.next[index]\n            this.prev[this.next[index]] = this.prev[index]\n          }\n          this.size--\n          this.free.push(index)\n        }\n      }\n    }\n    if (this.disposed) {\n      while (this.disposed.length) {\n        this.disposeAfter(...this.disposed.shift())\n      }\n    }\n    return deleted\n  }\n\n  clear() {\n    for (const index of this.rindexes({ allowStale: true })) {\n      const v = this.valList[index]\n      if (this.isBackgroundFetch(v)) {\n        v.__abortController.abort(new Error('deleted'))\n      } else {\n        const k = this.keyList[index]\n        this.dispose(v, k, 'delete')\n        if (this.disposeAfter) {\n          this.disposed.push([v, k, 'delete'])\n        }\n      }\n    }\n\n    this.keyMap.clear()\n    this.valList.fill(null)\n    this.keyList.fill(null)\n    if (this.ttls) {\n      this.ttls.fill(0)\n      this.starts.fill(0)\n    }\n    if (this.sizes) {\n      this.sizes.fill(0)\n    }\n    this.head = 0\n    this.tail = 0\n    this.initialFill = 1\n    this.free.length = 0\n    this.calculatedSize = 0\n    this.size = 0\n    if (this.disposed) {\n      while (this.disposed.length) {\n        this.disposeAfter(...this.disposed.shift())\n      }\n    }\n  }\n\n  get reset() {\n    deprecatedMethod('reset', 'clear')\n    return this.clear\n  }\n\n  get length() {\n    deprecatedProperty('length', 'size')\n    return this.size\n  }\n\n  static get AbortController() {\n    return AC\n  }\n  static get AbortSignal() {\n    return AS\n  }\n}\n\nmodule.exports = LRUCache\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/lru-cache@7.18.3/node_modules/lru-cache/index.js\n");

/***/ })

};
;