/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/sqlstring@2.3.3";
exports.ids = ["vendor-chunks/sqlstring@2.3.3"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/sqlstring@2.3.3/node_modules/sqlstring/index.js":
/*!****************************************************************************!*\
  !*** ./node_modules/.pnpm/sqlstring@2.3.3/node_modules/sqlstring/index.js ***!
  \****************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = __webpack_require__(/*! ./lib/SqlString */ \"(rsc)/./node_modules/.pnpm/sqlstring@2.3.3/node_modules/sqlstring/lib/SqlString.js\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vc3Fsc3RyaW5nQDIuMy4zL25vZGVfbW9kdWxlcy9zcWxzdHJpbmcvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQUEsaUpBQTJDIiwic291cmNlcyI6WyJFOlxcN21vdXRoTWlzc2lvblxcdW5pRmlnbWFcXGFkbWluLXN5c3RlbVxcbm9kZV9tb2R1bGVzXFwucG5wbVxcc3Fsc3RyaW5nQDIuMy4zXFxub2RlX21vZHVsZXNcXHNxbHN0cmluZ1xcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2xpYi9TcWxTdHJpbmcnKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/sqlstring@2.3.3/node_modules/sqlstring/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/sqlstring@2.3.3/node_modules/sqlstring/lib/SqlString.js":
/*!************************************************************************************!*\
  !*** ./node_modules/.pnpm/sqlstring@2.3.3/node_modules/sqlstring/lib/SqlString.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("var SqlString  = exports;\n\nvar ID_GLOBAL_REGEXP    = /`/g;\nvar QUAL_GLOBAL_REGEXP  = /\\./g;\nvar CHARS_GLOBAL_REGEXP = /[\\0\\b\\t\\n\\r\\x1a\\\"\\'\\\\]/g; // eslint-disable-line no-control-regex\nvar CHARS_ESCAPE_MAP    = {\n  '\\0'   : '\\\\0',\n  '\\b'   : '\\\\b',\n  '\\t'   : '\\\\t',\n  '\\n'   : '\\\\n',\n  '\\r'   : '\\\\r',\n  '\\x1a' : '\\\\Z',\n  '\"'    : '\\\\\"',\n  '\\''   : '\\\\\\'',\n  '\\\\'   : '\\\\\\\\'\n};\n\nSqlString.escapeId = function escapeId(val, forbidQualified) {\n  if (Array.isArray(val)) {\n    var sql = '';\n\n    for (var i = 0; i < val.length; i++) {\n      sql += (i === 0 ? '' : ', ') + SqlString.escapeId(val[i], forbidQualified);\n    }\n\n    return sql;\n  } else if (forbidQualified) {\n    return '`' + String(val).replace(ID_GLOBAL_REGEXP, '``') + '`';\n  } else {\n    return '`' + String(val).replace(ID_GLOBAL_REGEXP, '``').replace(QUAL_GLOBAL_REGEXP, '`.`') + '`';\n  }\n};\n\nSqlString.escape = function escape(val, stringifyObjects, timeZone) {\n  if (val === undefined || val === null) {\n    return 'NULL';\n  }\n\n  switch (typeof val) {\n    case 'boolean': return (val) ? 'true' : 'false';\n    case 'number': return val + '';\n    case 'object':\n      if (Object.prototype.toString.call(val) === '[object Date]') {\n        return SqlString.dateToString(val, timeZone || 'local');\n      } else if (Array.isArray(val)) {\n        return SqlString.arrayToList(val, timeZone);\n      } else if (Buffer.isBuffer(val)) {\n        return SqlString.bufferToString(val);\n      } else if (typeof val.toSqlString === 'function') {\n        return String(val.toSqlString());\n      } else if (stringifyObjects) {\n        return escapeString(val.toString());\n      } else {\n        return SqlString.objectToValues(val, timeZone);\n      }\n    default: return escapeString(val);\n  }\n};\n\nSqlString.arrayToList = function arrayToList(array, timeZone) {\n  var sql = '';\n\n  for (var i = 0; i < array.length; i++) {\n    var val = array[i];\n\n    if (Array.isArray(val)) {\n      sql += (i === 0 ? '' : ', ') + '(' + SqlString.arrayToList(val, timeZone) + ')';\n    } else {\n      sql += (i === 0 ? '' : ', ') + SqlString.escape(val, true, timeZone);\n    }\n  }\n\n  return sql;\n};\n\nSqlString.format = function format(sql, values, stringifyObjects, timeZone) {\n  if (values == null) {\n    return sql;\n  }\n\n  if (!Array.isArray(values)) {\n    values = [values];\n  }\n\n  var chunkIndex        = 0;\n  var placeholdersRegex = /\\?+/g;\n  var result            = '';\n  var valuesIndex       = 0;\n  var match;\n\n  while (valuesIndex < values.length && (match = placeholdersRegex.exec(sql))) {\n    var len = match[0].length;\n\n    if (len > 2) {\n      continue;\n    }\n\n    var value = len === 2\n      ? SqlString.escapeId(values[valuesIndex])\n      : SqlString.escape(values[valuesIndex], stringifyObjects, timeZone);\n\n    result += sql.slice(chunkIndex, match.index) + value;\n    chunkIndex = placeholdersRegex.lastIndex;\n    valuesIndex++;\n  }\n\n  if (chunkIndex === 0) {\n    // Nothing was replaced\n    return sql;\n  }\n\n  if (chunkIndex < sql.length) {\n    return result + sql.slice(chunkIndex);\n  }\n\n  return result;\n};\n\nSqlString.dateToString = function dateToString(date, timeZone) {\n  var dt = new Date(date);\n\n  if (isNaN(dt.getTime())) {\n    return 'NULL';\n  }\n\n  var year;\n  var month;\n  var day;\n  var hour;\n  var minute;\n  var second;\n  var millisecond;\n\n  if (timeZone === 'local') {\n    year        = dt.getFullYear();\n    month       = dt.getMonth() + 1;\n    day         = dt.getDate();\n    hour        = dt.getHours();\n    minute      = dt.getMinutes();\n    second      = dt.getSeconds();\n    millisecond = dt.getMilliseconds();\n  } else {\n    var tz = convertTimezone(timeZone);\n\n    if (tz !== false && tz !== 0) {\n      dt.setTime(dt.getTime() + (tz * 60000));\n    }\n\n    year       = dt.getUTCFullYear();\n    month       = dt.getUTCMonth() + 1;\n    day         = dt.getUTCDate();\n    hour        = dt.getUTCHours();\n    minute      = dt.getUTCMinutes();\n    second      = dt.getUTCSeconds();\n    millisecond = dt.getUTCMilliseconds();\n  }\n\n  // YYYY-MM-DD HH:mm:ss.mmm\n  var str = zeroPad(year, 4) + '-' + zeroPad(month, 2) + '-' + zeroPad(day, 2) + ' ' +\n    zeroPad(hour, 2) + ':' + zeroPad(minute, 2) + ':' + zeroPad(second, 2) + '.' +\n    zeroPad(millisecond, 3);\n\n  return escapeString(str);\n};\n\nSqlString.bufferToString = function bufferToString(buffer) {\n  return 'X' + escapeString(buffer.toString('hex'));\n};\n\nSqlString.objectToValues = function objectToValues(object, timeZone) {\n  var sql = '';\n\n  for (var key in object) {\n    var val = object[key];\n\n    if (typeof val === 'function') {\n      continue;\n    }\n\n    sql += (sql.length === 0 ? '' : ', ') + SqlString.escapeId(key) + ' = ' + SqlString.escape(val, true, timeZone);\n  }\n\n  return sql;\n};\n\nSqlString.raw = function raw(sql) {\n  if (typeof sql !== 'string') {\n    throw new TypeError('argument sql must be a string');\n  }\n\n  return {\n    toSqlString: function toSqlString() { return sql; }\n  };\n};\n\nfunction escapeString(val) {\n  var chunkIndex = CHARS_GLOBAL_REGEXP.lastIndex = 0;\n  var escapedVal = '';\n  var match;\n\n  while ((match = CHARS_GLOBAL_REGEXP.exec(val))) {\n    escapedVal += val.slice(chunkIndex, match.index) + CHARS_ESCAPE_MAP[match[0]];\n    chunkIndex = CHARS_GLOBAL_REGEXP.lastIndex;\n  }\n\n  if (chunkIndex === 0) {\n    // Nothing was escaped\n    return \"'\" + val + \"'\";\n  }\n\n  if (chunkIndex < val.length) {\n    return \"'\" + escapedVal + val.slice(chunkIndex) + \"'\";\n  }\n\n  return \"'\" + escapedVal + \"'\";\n}\n\nfunction zeroPad(number, length) {\n  number = number.toString();\n  while (number.length < length) {\n    number = '0' + number;\n  }\n\n  return number;\n}\n\nfunction convertTimezone(tz) {\n  if (tz === 'Z') {\n    return 0;\n  }\n\n  var m = tz.match(/([\\+\\-\\s])(\\d\\d):?(\\d\\d)?/);\n  if (m) {\n    return (m[1] === '-' ? -1 : 1) * (parseInt(m[2], 10) + ((m[3] ? parseInt(m[3], 10) : 0) / 60)) * 60;\n  }\n  return false;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/sqlstring@2.3.3/node_modules/sqlstring/lib/SqlString.js\n");

/***/ })

};
;